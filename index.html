<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">

    <!-- Mobile Optimization -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="GB Global">
    <meta name="theme-color" content="#4f46e5">
    <meta name="msapplication-TileColor" content="#4f46e5">
    <meta name="format-detection" content="telephone=no">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- SEO Meta Tags -->
    <title>GB Global - Premium Spare Parts for Home Appliances</title>
    <meta name="description" content="Your trusted source for premium quality spare parts for washing machines, microwaves, and car washers. Find exactly what you need, when you need it.">
    <meta name="keywords" content="spare parts, washing machine parts, microwave parts, car washer parts, appliance repair, GB Global">
    <meta name="author" content="GB Global">
    <meta name="robots" content="index, follow">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://gbglobal.com/">
    <meta property="og:title" content="GB Global - Premium Spare Parts for Home Appliances">
    <meta property="og:description" content="Your trusted source for premium quality spare parts for washing machines, microwaves, and car washers.">
    <meta property="og:image" content="/images/og-image.jpg">
    <meta property="og:site_name" content="GB Global">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://gbglobal.com/">
    <meta property="twitter:title" content="GB Global - Premium Spare Parts for Home Appliances">
    <meta property="twitter:description" content="Your trusted source for premium quality spare parts for washing machines, microwaves, and car washers.">
    <meta property="twitter:image" content="/images/og-image.jpg">

    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://images.unsplash.com">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Prevent FOUC (Flash of Unstyled Content) -->
    <style>
      #root {
        min-height: 100vh;
        background: linear-gradient(to bottom, #eef2ff, #ffffff);
      }

      /* Loading spinner for initial load */
      .initial-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, #eef2ff, #ffffff);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e0e7ff;
        border-top: 3px solid #4f46e5;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- Initial loading screen -->
    <div id="initial-loading" class="initial-loading">
      <div class="loading-spinner"></div>
    </div>

    <div id="root"></div>

    <script type="module" src="/src/main.jsx"></script>

    <!-- Remove loading screen once React app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingElement = document.getElementById('initial-loading');
          if (loadingElement) {
            loadingElement.style.opacity = '0';
            loadingElement.style.transition = 'opacity 0.3s ease-out';
            setTimeout(function() {
              loadingElement.remove();
            }, 300);
          }
        }, 500);
      });
    </script>
  </body>
</html>