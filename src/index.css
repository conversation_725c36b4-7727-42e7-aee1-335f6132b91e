@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced base styles for better cross-device compatibility */
@layer base {
  html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* Improved focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
    border-radius: 4px;
  }

  /* Better button and interactive element styles */
  button, [role="button"] {
    cursor: pointer;
    touch-action: manipulation;
  }

  /* Improved image handling */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Better form element styling */
  input, textarea, select {
    font-family: inherit;
  }
}

/* Enhanced component styles */
@layer components {
  /* Modern button styles */
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg;
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 border border-brand-300 text-base font-medium rounded-lg shadow-sm text-brand-700 bg-white hover:bg-brand-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 transition-all duration-200;
  }

  /* Enhanced card styles */
  .card {
    @apply bg-white rounded-xl shadow-card overflow-hidden transition-all duration-300 transform hover:-translate-y-1 hover:shadow-elevated;
  }

  /* Improved grid layouts */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8;
  }

  /* Better mobile navigation */
  .mobile-menu {
    @apply fixed inset-0 z-50 bg-white transform transition-transform duration-300 ease-in-out;
  }

  /* Enhanced dropdown styles */
  .dropdown-menu {
    @apply absolute z-50 mt-2 w-56 rounded-lg bg-white shadow-elevated ring-1 ring-black ring-opacity-5 focus:outline-none;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-gradient-to-r from-neutral-200 via-neutral-300 to-neutral-200 bg-[length:200%_100%];
    animation: skeleton-loading 1.5s infinite;
  }

  /* Improved search styles */
  .search-input {
    @apply w-full px-4 py-3 pl-10 pr-4 text-neutral-900 placeholder-neutral-500 bg-white border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent transition-all duration-200;
  }
}

/* Enhanced utility classes */
@layer utilities {
  /* Better spacing utilities */
  .space-y-safe > * + * {
    margin-top: clamp(0.5rem, 2vw, 1.5rem);
  }

  /* Responsive text utilities */
  .text-responsive {
    font-size: clamp(1rem, 2.5vw, 1.25rem);
  }

  .text-responsive-lg {
    font-size: clamp(1.25rem, 3vw, 2rem);
  }

  .text-responsive-xl {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }

  /* Safe area utilities for mobile devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Better touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smooth scrolling container */
  .scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}

/* Custom animations */
@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced mobile styles */
@media (max-width: 768px) {
  .mobile-optimized {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .mobile-spacing {
    margin-bottom: 1rem;
  }
}

/* iOS specific optimizations */
@supports (-webkit-touch-callout: none) {
  .ios-optimized {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
  }
}

/* Android specific optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .android-optimized {
    font-weight: 400;
  }
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Aspect ratio utilities for older browsers */
.aspect-w-16 {
  position: relative;
  padding-bottom: calc(9 / 16 * 100%);
}

.aspect-w-16 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* Better scrollbar for webkit browsers */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }
}